.container {
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px 12px;
  // border-bottom: 1px solid #F0F1F2;
}

.title {
  font-size: 14px;
  font-weight: 500;
  color: #1D2129;
  line-height: 22px;
}

.actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart {
  width: 100%;
  position: relative;
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #86909C;
  font-size: 14px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.8);
  padding: 8px 16px;
  border-radius: 4px;
}

.noData {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #86909C;
  font-size: 14px;
  z-index: 10;
}

// 响应式设计
@media (max-width: 768px) {
  .header {
    padding: 12px 16px 8px;
  }
  
  .title {
    font-size: 13px;
  }
  
  .chart {
    height: 250px !important;
  }
}
