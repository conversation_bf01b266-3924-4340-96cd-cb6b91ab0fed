// 空状态样式 - 按照Figma设计稿还原
.emptyContainer {
  position: relative;
  width: 100%;
  height: 360px;
  background: linear-gradient(135deg, #FFFFFF 0%, #D9E7FF 100%);
  border-radius: 6px;
  overflow: hidden;
  display: flex;
  align-items: center;
  padding: 40px;

  // 使用伪元素作为背景图片，避免z-index问题
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../../assets/images/bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: 0;
  }
}

.emptyContent {
  position: relative;
  z-index: 1;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.emptyContentInner {
  align-items: center;
  width: 100%;
}

.emptyTextSection {
  flex: 1;
  margin-right: 40px;
  max-width: fit-content;
}

.emptyTitle {
  font-size: 24px;
  font-weight: 500;
  line-height: 1.33;
  color: #151B26;
  margin-bottom: 24px;
}

.emptyDescription {
  font-size: 12px;
  font-weight: 400;
  line-height: 1.83;
  color: #151B26;
  margin-bottom: 74px;
  max-width: 700px;
}

.emptySubDescription {
  font-size: 12px;
  font-weight: 400;
  line-height: 1.83;
  color: #151B26;
  margin-bottom: 44px;

  a {
    color: #2468F2;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.emptyButton {
  width: 160px;
  height: 40px;
  background: #2468F2;
  border-radius: 4px;
  border: none;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.75;
  color: #FFFFFF;
  cursor: pointer;

  &:hover {
    background: #1557D6;
  }

  &:active {
    background: #0F4AC4;
  }
}

// 控制面板样式
.controlPanel {
  margin-bottom: 16px;
}

.controlRow {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.controlLabel {
  font-size: 12px;
  color: #5C5F66;
  margin-right: 8px;
  min-width: 80px;
}

.timeRangeGroup {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  justify-content: space-between; // 两端对齐
}

.timeControlsLeft {
  display: flex;
  align-items: center;
  gap: 8px;
}

.timeRangeButtons {
  :global(.acud-radio-button-wrapper) {
    border-radius: 0;
    border-right: none;
    
    &:first-child {
      border-radius: 4px 0 0 4px;
    }
    
    &:last-child {
      border-radius: 0 4px 4px 0;
      border-right: 1px solid #D4D6D9;
    }
    
    &:global(.acud-radio-button-wrapper-checked) {
      background: #E6F0FF;
      border-color: #2468F2;
      color: #2468F2;
    }
  }
}

.dimensionButtons {
  :global(.acud-radio-button-wrapper) {
    border-radius: 0;
    border-right: none;
    
    &:first-child {
      border-radius: 4px 0 0 4px;
    }
    
    &:last-child {
      border-radius: 0 4px 4px 0;
      border-right: 1px solid #D4D6D9;
    }
    
    &:global(.acud-radio-button-wrapper-checked) {
      background: #E6F0FF;
      border-color: #2468F2;
      color: #2468F2;
    }
  }
}

.customDatePicker {
  width: 339px;
  height: 32px;

  :global(.acud-picker) {
    border: 1px solid #D4D6D9;
    border-radius: 4px;

    &:hover {
      border-color: #2468F2;
    }

    &:focus-within {
      border-color: #2468F2;
      box-shadow: 0 0 0 2px rgba(36, 104, 242, 0.1);
    }
  }

  :global(.acud-picker-input) {
    font-size: 12px;
    color: #070C14;
  }

  :global(.acud-picker-separator) {
    color: #B8BABF;
  }
}

.refreshButton {
  width: 32px;
  height: 32px;
  padding: 2px;
  border: 1px solid #D4D6D9;
  background: #fff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    border-color: #2468F2;
  }
}

// 筛选条件样式
.filterRow {
  display: flex;
  align-items: center;
  gap: 32px;
  margin-top: 8px;
  flex-wrap: wrap;
}

.filterGroup {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filterLabel {
  font-size: 12px;
  width: 80px;
  color: #5C5F66;
  white-space: nowrap;
}

// 图表容器样式
.chartsContainer {
  display: grid;
  grid-template-columns: repeat(2, 1fr); // 固定2列布局
  gap: 16px;
  min-width: 800px; // 确保有足够空间显示2列
}

.chartWrapper {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #E8E9EB;
  overflow: hidden;
}

// 响应式设计
@media (max-width: 1200px) {
  .chartsContainer {
    grid-template-columns: 1fr; // 小屏幕时改为单列
  }

  .filterRow {
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .controlRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .controlLabel {
    min-width: auto;
  }
  
  .timeRangeGroup {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .timeControlsLeft {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .customDatePicker {
    width: 100%;
    max-width: 339px;
  }

  .refreshButton {
    align-self: flex-end;
  }
  
  .filterRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .filterGroup {
    width: 100%;
    justify-content: space-between;
  }
}

// 简单空状态样式
.emptyState {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #E8E9EB;

  p {
    font-size: 14px;
    color: #8C8E93;
    margin: 0;
  }
}
